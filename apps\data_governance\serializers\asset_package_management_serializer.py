#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : asset_package_management_serializer.py
<AUTHOR> JT_DA
@Date     : 2025/07/15
@File_Desc: 资产包管理序列化器
"""

import os
import re
import pandas as pd
from rest_framework import serializers
from django.db import transaction
from django.utils import timezone

from apps.data_governance.models import (
    AssetPackageManagement,
    AssetPackageFieldMapping,
    AssetPackageFieldConfig,
    AssetPackageManagementFile
)
from apps.counterparty.models import CreditorBasicInfo, DebtorBasicInfo
from apps.mediation_management.models import MediationCase
from ops_management.constant import VALIDATION_FUNCTION_MAPPING


class NullablePrimaryKeyRelatedField(serializers.PrimaryKeyRelatedField):
    """
    支持multipart/form-data空值处理的PrimaryKeyRelatedField

    处理前端表单提交时的空值情况：
    - 字符串 "null" → None
    - 空字符串 "" → None
    - 不传递字段 → None (通过required=False)
    - 有效主键值 → 正常处理
    """

    def to_internal_value(self, data):
        """将输入数据转换为内部值，处理空值情况"""
        # 处理字符串形式的空值
        if data == "null" or data == "" or data is None:
            return None

        # 处理字符串形式的数字ID
        if isinstance(data, str) and data.strip():
            try:
                # 尝试将字符串转换为整数
                data = int(data.strip())
            except (ValueError, TypeError):
                # 如果转换失败，让父类处理错误
                pass

        # 调用父类方法处理正常情况
        return super().to_internal_value(data)


class AssetPackageManagementFileSerializer(serializers.ModelSerializer):
    """资产包管理附件文件序列化器 - 用于附件详情展示"""

    class Meta:
        model = AssetPackageManagementFile
        fields = ["id", "file_name", "file"]


class AssetPackageManagementListSerializer(serializers.ModelSerializer):
    """资产包管理列表序列化器"""
    # 关联字段的中文显示
    uploader_name = serializers.CharField(source='uploader.username', read_only=True)
    creditor_name = serializers.CharField(source='creditor.creditor_name', read_only=True)

    # 资产包状态中文显示
    package_status_cn = serializers.CharField(source='get_package_status_display', read_only=True)

    # 上传时间格式化显示 - 格式化为人类可读的时间格式
    upload_time = serializers.DateTimeField(format='%Y-%m-%d %H:%M:%S', read_only=True)

    # 原文件名显示 - 从文件路径中提取文件名
    source_file_name = serializers.SerializerMethodField()

    # 文件大小格式化显示
    file_size_display = serializers.SerializerMethodField()

    # 字段映射数量统计
    mapping_count = serializers.SerializerMethodField()

    # 字段映射详情
    field_mappings_detail = serializers.SerializerMethodField()

    # 映射字段名称列表
    mapped_field_names = serializers.SerializerMethodField()

    # 附件文件信息
    attachments = AssetPackageManagementFileSerializer(many=True, read_only=True)
    attachments_count = serializers.SerializerMethodField()
    file_cn = serializers.SerializerMethodField(read_only=True, help_text="附件（中文描述）")

    # 调解信息配置
    mediation_config = serializers.JSONField(read_only=True, help_text="调解信息配置，JSON格式")

    class Meta:
        model = AssetPackageManagement
        fields = [
            'id', 'package_name', 'source_file', 'source_file_name', 'file_size', 'file_size_display',
            'uploader', 'uploader_name', 'upload_time', 'creditor', 'creditor_name',
            'package_status', 'package_status_cn', 'unavailable_reason',
            'mapping_count', 'field_mappings_detail', 'mapped_field_names',
            'attachments', 'attachments_count', 'file_cn', 'mediation_config',
            'created_time', 'updated_time'
        ]
    
    def get_source_file_name(self, obj):
        """获取原文件名 - 从文件路径中提取文件名"""
        if not obj.source_file:
            return "无文件"
        return os.path.basename(obj.source_file.name)

    def get_file_size_display(self, obj):
        """格式化文件大小显示"""
        if not obj.file_size:
            return "未知"

        size = obj.file_size
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size / 1024:.1f} KB"
        elif size < 1024 * 1024 * 1024:
            return f"{size / (1024 * 1024):.1f} MB"
        else:
            return f"{size / (1024 * 1024 * 1024):.1f} GB"
    
    def get_mapping_count(self, obj):
        """获取字段映射数量"""
        return obj.field_mappings.count()

    def get_field_mappings_detail(self, obj):
        """获取字段映射详情"""
        mappings = obj.field_mappings.all()

        mapping_data = []
        for mapping in mappings:
            mapping_data.append({
                'id': mapping.id,
                'original_field_name': mapping.original_field_name,
                'mapped_field_config': {
                    'id': mapping.mapped_field_config.id,
                    'field_name': mapping.mapped_field_config.field_name,
                    'field_type': mapping.mapped_field_config.field_type,
                    'field_type_cn': mapping.mapped_field_config.get_field_type_display(),
                    'data_validation': mapping.mapped_field_config.data_validation,
                    'is_masked': mapping.mapped_field_config.is_masked,
                } if mapping.mapped_field_config else None,
                # 新增：映射的债务人字段配置
                'mapped_debtor_field_config': mapping.mapped_debtor_field_config,
                'mapped_debtor_field_config_cn': mapping.get_mapped_debtor_field_config_display() if mapping.mapped_debtor_field_config else None
            })

        return mapping_data

    def get_mapped_field_names(self, obj):
        """获取映射字段名称列表"""
        mappings = obj.field_mappings.all()

        field_names = []
        for mapping in mappings:
            # 只提取有映射配置的字段名称
            if mapping.mapped_field_config:
                field_names.append(mapping.mapped_field_config.field_name)

        return field_names

    def get_attachments_count(self, obj):
        """获取附件文件数量"""
        return obj.attachments.count()

    def get_file_cn(self, obj):
        """获取附件文件中文显示名称列表"""
        # 序列化相关的附件文件实例
        serializer = AssetPackageManagementFileSerializer(obj.attachments.all(), many=True)
        serialized_data = serializer.data

        # 检查是否有附件文件
        if serialized_data:
            # 如果只有一个文件，直接返回其名称
            if len(serialized_data) == 1:
                return serialized_data[0]["file_name"]
            else:
                # 如果有多个文件，用索引连接它们的名称
                return "\n".join([f"{idx + 1}、{item['file_name']}" for idx, item in enumerate(serialized_data)])
        else:
            return ""


class AssetPackageManagementCreateSerializer(serializers.ModelSerializer):
    """资产包管理创建序列化器"""

    # 接收文件参数，使用file而非excel_file
    file = serializers.FileField(write_only=True, help_text="上传的Excel文件")

    # 债权人字段 - 支持multipart/form-data空值处理
    creditor = NullablePrimaryKeyRelatedField(
        queryset=CreditorBasicInfo.objects.all(),
        required=False,
        allow_null=True,
        help_text="关联的债权人信息，可选字段，支持字符串'null'、空字符串''和真正的null值"
    )

    class Meta:
        model = AssetPackageManagement
        fields = ['package_name', 'file', 'creditor']
    
    def validate_file(self, value):
        """验证上传的文件"""
        # 检查文件扩展名
        allowed_extensions = ['.xlsx', '.xls']
        file_extension = os.path.splitext(value.name)[1].lower()
        
        if file_extension not in allowed_extensions:
            raise serializers.ValidationError("只支持Excel文件格式(.xlsx, .xls)")
        
        # 检查文件大小（限制为50MB）
        max_size = 50 * 1024 * 1024  # 50MB
        if value.size > max_size:
            raise serializers.ValidationError("文件大小不能超过50MB")
        
        return value
    
    def validate_package_name(self, value):
        """验证资产包名称"""
        if not value or not value.strip():
            raise serializers.ValidationError("资产包名称不能为空")
        
        # 检查名称是否已存在
        if AssetPackageManagement.objects.filter(package_name=value.strip()).exists():
            raise serializers.ValidationError("该资产包名称已存在")
        
        return value.strip()
    
    def create(self, validated_data):
        """创建资产包管理记录并处理Excel文件"""
        file = validated_data.pop('file')
        
        with transaction.atomic():
            # 创建资产包管理记录
            asset_package = AssetPackageManagement.objects.create(
                package_name=validated_data['package_name'],
                source_file=file,
                file_size=file.size,
                uploader=validated_data.get('uploader'),
                upload_time=timezone.now(),  # 设置上传时间为当前时间
                creditor=validated_data.get('creditor'),
                unavailable_reason="资产包已创建，请编辑字段映射配置后方可使用"  # 设置默认不可用原因
            )
            
            # 处理Excel文件，提取表头并创建字段映射
            self._process_excel_file(asset_package, file)
            
            return asset_package
    
    def _process_excel_file(self, asset_package, file):
        """处理Excel文件，提取表头并创建字段映射"""
        try:
            # 使用pandas读取Excel文件的表头
            df = pd.read_excel(file, nrows=0)  # 只读取表头，不读取数据
            excel_columns = df.columns.tolist()

            # 为每个Excel表头字段创建映射记录
            field_mappings = []
            unmatched_fields = []

            for column_name in excel_columns:
                # 尝试通过字段名称匹配AssetPackageFieldConfig
                try:
                    field_config = AssetPackageFieldConfig.objects.get(
                        field_name=column_name
                    )
                    # 创建字段映射记录（mapped_debtor_field_config 字段保持为空，由用户后续配置）
                    field_mapping = AssetPackageFieldMapping.objects.create(
                        original_field_name=column_name,
                        mapped_field_config=field_config,
                        mapped_debtor_field_config=None  # 新字段初始为空，允许用户后续配置
                    )
                    field_mappings.append(field_mapping)

                except AssetPackageFieldConfig.DoesNotExist:
                    # 记录未匹配的字段，供后续手动配置
                    unmatched_fields.append(column_name)
                    continue

            # 建立多对多关联关系
            if field_mappings:
                asset_package.field_mappings.set(field_mappings)

            # 记录处理结果到日志（可选）
            if unmatched_fields:
                print(f"资产包 '{asset_package.package_name}' 中以下字段未找到匹配配置: {unmatched_fields}")

        except Exception as e:
            # 如果Excel处理失败，删除已创建的资产包记录
            asset_package.delete()
            raise serializers.ValidationError(f"Excel文件处理失败: {str(e)}")


class AssetPackageManagementUpdateSerializer(serializers.ModelSerializer):
    """资产包管理更新序列化器"""

    # 字段映射数据，支持新增、修改、删除字段映射记录
    field_mappings = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        write_only=True,
        help_text="字段映射数据数组，每个记录包含id(可选)、original_field_name、mapped_field_config_id、mapped_debtor_field_config(可选)"
    )

    class Meta:
        model = AssetPackageManagement
        fields = ['package_name', 'creditor', 'upload_time', 'field_mappings']

    def validate_package_name(self, value):
        """验证资产包名称"""
        if not value or not value.strip():
            raise serializers.ValidationError("资产包名称不能为空")

        # 检查名称是否已存在（排除当前记录）
        instance = self.instance
        if AssetPackageManagement.objects.filter(
            package_name=value.strip()
        ).exclude(id=instance.id).exists():
            raise serializers.ValidationError("该资产包名称已存在")

        return value.strip()

    def validate_field_mappings(self, value):
        """验证字段映射数据 - 只支持修改现有字段映射"""
        if not value:
            return value

        # 验证每个映射记录的数据格式
        mapping_ids = []

        for i, mapping_data in enumerate(value):
            # 验证必需字段
            if 'id' not in mapping_data:
                raise serializers.ValidationError(f"第{i+1}条映射记录缺少id字段，只支持修改现有字段映射")

            if 'original_field_name' not in mapping_data:
                raise serializers.ValidationError(f"第{i+1}条映射记录缺少original_field_name字段")

            if 'mapped_field_config_id' not in mapping_data:
                raise serializers.ValidationError(f"第{i+1}条映射记录缺少mapped_field_config_id字段")

            mapping_id = mapping_data['id']
            original_field_name = mapping_data['original_field_name']
            mapped_field_config_id = mapping_data['mapped_field_config_id']
            mapped_debtor_field_config = mapping_data.get('mapped_debtor_field_config')  # 新字段，可选

            # 验证映射ID不为空
            if not mapping_id:
                raise serializers.ValidationError(f"第{i+1}条映射记录的id不能为空")

            # 验证original_field_name不为空
            if not original_field_name or not original_field_name.strip():
                raise serializers.ValidationError(f"第{i+1}条映射记录的original_field_name不能为空")

            # 验证mapped_field_config_id对应的记录存在
            if mapped_field_config_id:
                try:
                    AssetPackageFieldConfig.objects.get(id=mapped_field_config_id)
                except AssetPackageFieldConfig.DoesNotExist:
                    raise serializers.ValidationError(f"第{i+1}条映射记录的mapped_field_config_id({mapped_field_config_id})对应的字段配置不存在")

            # 验证mapped_debtor_field_config字段值的有效性（如果提供）
            if mapped_debtor_field_config:
                # 导入模型以获取选择项
                from apps.data_governance.models import AssetPackageFieldMapping
                valid_choices = [choice[0] for choice in AssetPackageFieldMapping.DEBTOR_FIELD_CHOICES]
                if mapped_debtor_field_config not in valid_choices:
                    raise serializers.ValidationError(f"第{i+1}条映射记录的mapped_debtor_field_config值({mapped_debtor_field_config})无效，有效选项: {valid_choices}")

            # 检查重复的映射ID
            if mapping_id in mapping_ids:
                raise serializers.ValidationError(f"映射记录ID({mapping_id})存在重复")

            mapping_ids.append(mapping_id)

        return value

    def update(self, instance, validated_data):
        """更新资产包管理记录，支持字段映射的编辑"""
        field_mappings_data = validated_data.pop('field_mappings', None)

        # 更新基本字段（package_name、creditor）
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # 如果提供了字段映射数据，则更新字段映射关系
        if field_mappings_data is not None:
            self._update_field_mappings(instance, field_mappings_data)

            # 保存字段映射关系后，执行数据验证并更新资产包状态
            self._validate_excel_data_and_update_status(instance)

            # 重新加载实例以获取最新状态
            instance.refresh_from_db()

            # 如果资产包状态为可用，则执行债务人数据导入
            if instance.package_status == 'available':
                print(f"资产包 '{instance.package_name}' 状态为可用，开始执行债务人数据导入")
                self._import_debtor_data_from_excel(instance)
            else:
                print(f"资产包 '{instance.package_name}' 状态为 '{instance.package_status}'，跳过债务人数据导入")

        return instance

    def _update_field_mappings(self, instance, field_mappings_data):
        """更新字段映射关系 - 只支持修改现有字段的映射配置"""
        with transaction.atomic():
            # 获取当前所有的字段映射记录
            existing_mappings = {
                mapping.id: mapping
                for mapping in instance.field_mappings.all()
            }

            # 处理每个字段映射数据 - 只更新现有映射记录
            for mapping_data in field_mappings_data:
                mapping_id = mapping_data.get('id')

                # 必须提供映射ID，因为只支持修改现有映射
                if not mapping_id or mapping_id not in existing_mappings:
                    raise serializers.ValidationError(f"映射记录ID({mapping_id})不存在，只支持修改现有字段映射")

                original_field_name = mapping_data['original_field_name'].strip()
                mapped_field_config_id = mapping_data['mapped_field_config_id']
                mapped_debtor_field_config = mapping_data.get('mapped_debtor_field_config')  # 新字段，可选

                # 获取字段配置对象（允许为null）
                field_config = None
                if mapped_field_config_id:
                    field_config = AssetPackageFieldConfig.objects.get(id=mapped_field_config_id)

                # 更新现有映射记录的字段配置
                mapping = existing_mappings[mapping_id]
                # 原字段名称不允许修改，因为它来自Excel表头
                if mapping.original_field_name != original_field_name:
                    raise serializers.ValidationError(f"不允许修改原字段名称，Excel表头字段'{mapping.original_field_name}'是固定的")

                # 更新映射的字段配置（允许设置为null）
                mapping.mapped_field_config = field_config
                # 更新映射的债务人字段配置（允许设置为null或空字符串）
                mapping.mapped_debtor_field_config = mapped_debtor_field_config if mapped_debtor_field_config else None
                mapping.save()

    def _validate_excel_data_and_update_status(self, instance):
        """
        验证Excel文件数据并更新资产包状态

        Args:
            instance: AssetPackageManagement实例
        """
        try:
            # 检查是否有原文件
            if not instance.source_file:
                instance.package_status = 'unavailable'
                instance.unavailable_reason = '缺少原文件，无法进行数据验证'
                instance.save()
                return

            # 使用pandas重新读取Excel文件数据
            df = pd.read_excel(instance.source_file.path)

            # 获取所有字段映射关系
            field_mappings = instance.field_mappings.all()

            # 收集验证失败的记录
            validation_errors = []

            # 遍历所有字段映射关系，对需要验证的字段进行数据验证
            for mapping in field_mappings:
                # 只对有映射配置且需要验证的字段进行验证
                if (mapping.mapped_field_config and
                    mapping.mapped_field_config.data_validation and
                    mapping.mapped_field_config.data_validation != 'none'):

                    # 获取验证函数
                    validation_type = mapping.mapped_field_config.data_validation
                    validation_function = VALIDATION_FUNCTION_MAPPING.get(validation_type)

                    if validation_function:
                        # 检查Excel文件中是否存在该列
                        if mapping.original_field_name in df.columns:
                            column_data = df[mapping.original_field_name]

                            # 对该列的每个数据值执行验证
                            for row_index, cell_value in column_data.items():
                                # 跳过空值
                                if pd.isna(cell_value) or cell_value == '':
                                    continue

                                # 执行验证
                                try:
                                    is_valid = validation_function(str(cell_value))
                                    if not is_valid:
                                        validation_errors.append({
                                            'row': row_index + 2,  # Excel行号从2开始（第1行是表头）
                                            'column': mapping.original_field_name,
                                            'value': str(cell_value),
                                            'error': f'{mapping.mapped_field_config.get_data_validation_display()}失败'
                                        })
                                except Exception as e:
                                    validation_errors.append({
                                        'row': row_index + 2,
                                        'column': mapping.original_field_name,
                                        'value': str(cell_value),
                                        'error': f'验证过程异常: {str(e)}'
                                    })

            # 根据验证结果更新资产包状态
            if validation_errors:
                # 存在验证失败的数据，设置为不可用
                instance.package_status = 'unavailable'

                # 格式化验证失败信息
                error_summary = f"数据验证失败，共发现 {len(validation_errors)} 个问题：\n\n"

                # 按列分组显示错误
                errors_by_column = {}
                for error in validation_errors:
                    column = error['column']
                    if column not in errors_by_column:
                        errors_by_column[column] = []
                    errors_by_column[column].append(error)

                for column, column_errors in errors_by_column.items():
                    error_summary += f"【{column}】字段验证失败 ({len(column_errors)} 个)：\n"
                    for error in column_errors[:5]:  # 每列最多显示5个错误示例
                        error_summary += f"  - 第{error['row']}行: {error['value']} ({error['error']})\n"
                    if len(column_errors) > 5:
                        error_summary += f"  - 还有 {len(column_errors) - 5} 个类似错误...\n"
                    error_summary += "\n"

                instance.unavailable_reason = error_summary
            else:
                # 所有数据验证通过，设置为可用
                instance.package_status = 'available'
                instance.unavailable_reason = None

            instance.save()

        except Exception as e:
            # 验证过程中出现异常，设置为不可用
            instance.package_status = 'unavailable'
            instance.unavailable_reason = f'数据验证过程中出现异常: {str(e)}'
            instance.save()

    def _import_debtor_data_from_excel(self, instance):
        """
        从Excel文件导入债务人数据到DebtorBasicInfo模型

        功能说明：
        1. 检查是否存在mapped_debtor_field_config为"id_number"的字段映射
        2. 如果不存在，跳过整个导入过程
        3. 读取Excel文件内容，根据字段映射关系解析数据
        4. 通过证件号码去重，避免创建重复记录
        5. 创建DebtorBasicInfo记录并处理异常情况

        Args:
            instance: AssetPackageManagement实例
        """
        try:
            # 检查是否存在id_number字段映射，这是导入的必要条件
            id_number_mapping = instance.field_mappings.filter(
                mapped_debtor_field_config='id_number'
            ).first()

            if not id_number_mapping:
                print(f"资产包 '{instance.package_name}' 未配置证件号码字段映射，跳过债务人数据导入")
                return

            # 检查原文件是否存在
            if not instance.source_file:
                print(f"资产包 '{instance.package_name}' 原文件不存在，无法导入债务人数据")
                return

            # 获取所有字段映射关系，构建字段映射字典
            field_mappings = instance.field_mappings.filter(
                mapped_debtor_field_config__isnull=False
            ).exclude(mapped_debtor_field_config='')

            if not field_mappings.exists():
                print(f"资产包 '{instance.package_name}' 未配置任何债务人字段映射，跳过数据导入")
                return

            # 构建Excel列名到债务人字段的映射字典
            column_to_field_mapping = {}
            for mapping in field_mappings:
                column_to_field_mapping[mapping.original_field_name] = mapping.mapped_debtor_field_config

            print(f"资产包 '{instance.package_name}' 开始导入债务人数据，字段映射: {column_to_field_mapping}")

            # 使用pandas读取Excel文件
            df = pd.read_excel(instance.source_file.path)

            # 检查必需的列是否存在
            missing_columns = []
            for excel_column in column_to_field_mapping.keys():
                if excel_column not in df.columns:
                    missing_columns.append(excel_column)

            if missing_columns:
                print(f"资产包 '{instance.package_name}' Excel文件缺少必需列: {missing_columns}")
                return

            # 获取已存在的证件号码，用于去重
            existing_id_numbers = set(
                DebtorBasicInfo.objects.filter(
                    id_number__isnull=False
                ).exclude(id_number='').values_list('id_number', flat=True)
            )

            created_count = 0
            skipped_count = 0
            error_count = 0

            # 逐行处理Excel数据
            for index, row in df.iterrows():
                try:
                    # 构建债务人数据字典
                    debtor_data = {}

                    # 根据字段映射提取数据
                    for excel_column, debtor_field in column_to_field_mapping.items():
                        cell_value = row[excel_column]

                        # 处理空值和NaN
                        if pd.isna(cell_value) or cell_value == '' or str(cell_value).strip() == '':
                            debtor_data[debtor_field] = None
                        else:
                            # 转换为字符串并去除首尾空格
                            debtor_data[debtor_field] = str(cell_value).strip()

                    # 检查证件号码是否为空
                    id_number = debtor_data.get('id_number')
                    if not id_number:
                        print(f"第{index+2}行：证件号码为空，跳过该记录")
                        skipped_count += 1
                        continue

                    # 检查证件号码是否已存在
                    if id_number in existing_id_numbers:
                        print(f"第{index+2}行：证件号码 '{id_number}' 已存在，跳过该记录")
                        skipped_count += 1
                        continue

                    # 创建DebtorBasicInfo记录
                    DebtorBasicInfo.objects.create(**debtor_data)
                    existing_id_numbers.add(id_number)  # 添加到已存在集合中，避免同一批次内重复
                    created_count += 1

                    print(f"第{index+2}行：成功创建债务人记录，证件号码: {id_number}")

                except Exception as row_error:
                    error_count += 1
                    print(f"第{index+2}行：创建债务人记录失败: {str(row_error)}")
                    continue

            # 输出导入结果统计
            print(f"资产包 '{instance.package_name}' 债务人数据导入完成:")
            print(f"  - 成功创建: {created_count} 条记录")
            print(f"  - 跳过记录: {skipped_count} 条记录")
            print(f"  - 错误记录: {error_count} 条记录")

        except Exception as e:
            print(f"资产包 '{instance.package_name}' 债务人数据导入过程中出现异常: {str(e)}")
            # 不抛出异常，避免影响主流程


class AssetPackageManagementAttachmentUpdateSerializer(serializers.ModelSerializer):
    """资产包管理附件和调解配置专用更新序列化器 - 仅处理 attachments 和 mediation_config 字段"""

    mediation_config = serializers.JSONField(
        required=False, allow_null=True, help_text="调解信息配置，JSON格式"
    )

    # 支持多文件上传 - 接收文件列表用于创建附件
    file = serializers.ListField(
        child=serializers.FileField(),
        required=False,
        write_only=True,
        help_text="上传的附件文件列表，支持多文件上传"
    )

    # 支持附件ID列表 - 用于保留现有附件
    file_id = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        write_only=True,
        help_text="需要保留的附件ID列表"
    )

    class Meta:
        model = AssetPackageManagement
        fields = ["mediation_config", "file", "file_id"]

    def validate_mediation_config(self, value):
        """验证调解信息配置格式"""
        if value is None:
            return value

        # 如果是字符串，尝试解析为JSON验证格式
        if isinstance(value, str):
            try:
                import json
                json.loads(value)
            except json.JSONDecodeError:
                raise serializers.ValidationError("调解信息配置必须是有效的JSON格式")

        return value

    def validate_file_id(self, value):
        """验证附件ID列表的有效性"""
        if value:
            # 检查所有附件ID是否存在
            existing_ids = set(
                AssetPackageManagementFile.objects.filter(id__in=value).values_list("id", flat=True)
            )
            invalid_ids = set(value) - existing_ids
            if invalid_ids:
                raise serializers.ValidationError(f"以下附件ID不存在：{list(invalid_ids)}")
        return value

    def validate_file(self, value):
        """验证上传的附件文件"""
        if not value:
            return value

        for file_data in value:
            # 检查文件大小（限制为10MB）
            max_size = 10 * 1024 * 1024  # 10MB
            if file_data.size > max_size:
                raise serializers.ValidationError(f"文件 '{file_data.name}' 大小不能超过10MB")

        return value

    def update(self, instance, validated_data):
        """更新资产包附件和调解配置信息"""
        # 提取文件相关数据
        file_ids = validated_data.pop("file_id", [])
        files_data = validated_data.pop("file", [])

        with transaction.atomic():
            try:
                # 更新调解配置字段
                if "mediation_config" in validated_data:
                    instance.mediation_config = validated_data["mediation_config"]
                    instance.save()

                # 处理附件关联关系
                if file_ids is not None:  # 如果提供了file_id列表，则重置附件关联
                    # 获取有效的附件对象
                    existing_attachments = AssetPackageManagementFile.objects.filter(id__in=file_ids)
                    instance.attachments.set(existing_attachments)

                # 处理新上传的附件文件
                if files_data:
                    new_attachment_files = []
                    for file_data in files_data:
                        # 创建附件记录
                        attachment = AssetPackageManagementFile.objects.create(
                            file=file_data,
                            file_name=file_data.name
                        )
                        new_attachment_files.append(attachment)

                    # 追加新的附件到现有关联关系中
                    instance.attachments.add(*new_attachment_files)

                # 当更新资产包附件和调解配置信息完成后，自动创建 MediationCase 记录
                self._auto_create_mediation_cases(instance)

                return instance

            except Exception as e:
                # 记录错误并重新抛出
                raise serializers.ValidationError(f"更新资产包附件和调解配置时发生错误：{str(e)}")

    def _auto_create_mediation_cases(self, instance):
        """
        自动创建 MediationCase 记录

        触发条件：当更新资产包附件和调解配置信息完成后

        主要功能：
        1. 读取资产包原文件（xlsx）
        2. 解析文件内容，获取所有数据行
        3. 为每行数据创建一条 MediationCase 记录
        4. 重复记录检查，避免重复创建

        字段映射规则：
        - debtor: 通过资产包的字段映射配置查找债务人字段，如果映射配置中存在 "id_number" 列配置，
                 则使用该行对应列的值在 DebtorBasicInfo 模型中通过 "id_number" 字段查询获取债务人对象，
                 如果不存在 "id_number" 映射或查询无结果，则设置为 NULL
        - creditor: 直接使用当前资产包的 creditor 字段值
        - asset_package: 设置为当前正在更新的资产包对象
        - asset_package_row_number: 设置为 xlsx 文件中的数据行号（从 1 开始计数，不包含表头行）

        Args:
            instance: AssetPackageManagement 实例
        """
        try:
            # 重复记录检查：如果已存在相关记录，则跳过整个创建过程
            existing_cases = MediationCase.objects.filter(asset_package=instance)
            if existing_cases.exists():
                print(f"资产包 '{instance.package_name}' 已存在 {existing_cases.count()} 条 MediationCase 记录，跳过自动创建")
                return

            # 检查原文件是否存在
            if not instance.source_file:
                print(f"资产包 '{instance.package_name}' 原文件不存在，无法自动创建 MediationCase 记录")
                return

            print(f"资产包 '{instance.package_name}' 开始自动创建 MediationCase 记录")

            # 使用 pandas 读取 Excel 文件内容
            df = pd.read_excel(instance.source_file.path)

            # 获取 id_number 字段映射配置（用于查找债务人）
            id_number_mapping = instance.field_mappings.filter(
                mapped_debtor_field_config='id_number'
            ).first()

            id_number_column = None
            if id_number_mapping:
                id_number_column = id_number_mapping.original_field_name
                print(f"找到证件号码字段映射: {id_number_column}")
            else:
                print("未找到证件号码字段映射，债务人字段将设置为 NULL")

            created_count = 0
            error_count = 0

            # 遍历 Excel 文件的每一行数据（跳过表头）
            for index, row in df.iterrows():
                try:
                    # 查找债务人对象
                    debtor = None
                    if id_number_column and id_number_column in df.columns:
                        id_number_value = row[id_number_column]

                        # 处理空值和 NaN
                        if pd.notna(id_number_value) and str(id_number_value).strip():
                            id_number_str = str(id_number_value).strip()
                            try:
                                debtor = DebtorBasicInfo.objects.get(id_number=id_number_str)
                                print(f"第{index+2}行：找到债务人，证件号码: {id_number_str}")
                            except DebtorBasicInfo.DoesNotExist:
                                print(f"第{index+2}行：未找到债务人，证件号码: {id_number_str}")
                            except DebtorBasicInfo.MultipleObjectsReturned:
                                print(f"第{index+2}行：证件号码 {id_number_str} 对应多个债务人，使用第一个")
                                debtor = DebtorBasicInfo.objects.filter(id_number=id_number_str).first()

                    # 创建 MediationCase 记录
                    # 注意：mediation_config 字段已从模型中移除，调解配置信息现在通过关联的资产包获取
                    mediation_case = MediationCase.objects.create(
                        debtor=debtor,
                        creditor=instance.creditor,
                        asset_package=instance,
                        asset_package_row_number=index + 1,  # 从 1 开始计数，不包含表头行
                    )

                    created_count += 1
                    print(f"第{index+2}行：成功创建 MediationCase 记录，ID: {mediation_case.id}")

                except Exception as row_error:
                    error_count += 1
                    print(f"第{index+2}行：创建 MediationCase 记录失败: {str(row_error)}")
                    continue

            # 输出创建结果统计
            print(f"资产包 '{instance.package_name}' MediationCase 记录创建完成:")
            print(f"  - 成功创建: {created_count} 条记录")
            print(f"  - 错误记录: {error_count} 条记录")

        except Exception as e:
            print(f"资产包 '{instance.package_name}' 自动创建 MediationCase 记录过程中出现异常: {str(e)}")
            # 不抛出异常，避免影响主流程


class AssetPackageExpressionCalculationSerializer(serializers.Serializer):
    """资产包表达式计算序列化器"""

    # 逻辑类型选择项
    LOGIC_TYPE_CHOICES = [
        ("text_format", "文本格式化"),
        ("result_calculation", "结果运算"),
    ]

    asset_package_id = serializers.IntegerField(
        required=True,
        help_text="资产包ID，用于查询对应的资产包对象"
    )
    asset_package_row_number = serializers.IntegerField(
        required=False,
        default=1,
        min_value=1,
        help_text="资产包行号，默认为1，用于指定计算数据的行"
    )
    expression = serializers.CharField(
        required=True,
        max_length=1000,
        help_text="计算表达式，格式示例：{债权总额（折人民币）}-{本金余额（折人民币）}"
    )
    logic_type = serializers.ChoiceField(
        choices=LOGIC_TYPE_CHOICES,
        required=True,
        help_text="逻辑类型：文本格式化（仅进行字符串格式化）或结果运算（格式化后使用eval计算表达式）"
    )

    def validate_asset_package_id(self, value):
        """验证资产包ID是否存在"""
        try:
            AssetPackageManagement.objects.get(id=value)
        except AssetPackageManagement.DoesNotExist:
            raise serializers.ValidationError("指定的资产包不存在")
        return value

    def validate_expression(self, value):
        """验证表达式格式"""
        if not value.strip():
            raise serializers.ValidationError("表达式不能为空")

        # 检查是否包含花括号变量
        # if not re.search(r'\{[^}]+\}', value):
        #     raise serializers.ValidationError("表达式必须包含至少一个花括号变量，如：{字段名}")

        return value
