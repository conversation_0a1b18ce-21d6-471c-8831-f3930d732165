#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : urls.py
<AUTHOR> JT_DA
@Date     : 2025/07/03
@File_Desc:
"""

from django.urls import path

from apps.user.views import auth_views, log_views, mediator_views, wechat_auth_views

urlpatterns = [
    # 用户认证
    path("user_info/", auth_views.UserInfo.as_view()),
    # 刷新token
    path("token/", auth_views.GetNewAccessToken.as_view()),
    # 退出登录
    path("logout/", auth_views.UserLogout.as_view()),
    # 修改密码
    path("change_password/", auth_views.ChangePassword.as_view()),
    # 写入操作日志
    path("operation_log/", log_views.OperationLog.as_view()),
    # 调解员列表
    path("mediators/", mediator_views.MediatorListView.as_view()),
    # 微信登录
    path("wechat/login/", wechat_auth_views.WechatLoginView.as_view()),
    # 微信刷新令牌
    path("wechat/refresh/", wechat_auth_views.WechatRefreshView.as_view()),
]
