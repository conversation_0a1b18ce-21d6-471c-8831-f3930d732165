#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : mediation_case_views.py
<AUTHOR> JT_DA
@Date     : 2025/07/22
@File_Desc: 调解案件相关视图
"""

import logging
from django.db.models import Q
from rest_framework.generics import GenericAPIView
from rest_framework import serializers

from apps.mediation_management.models import MediationCase, MediationPlan
from apps.counterparty.models import DebtorBasicInfo
from apps.mediation_management.serializers.mediation_case_query_serializers import MediationCaseByNumberSerializer, MediationCaseByDebtorSerializer, MediationContentSerializer, MediationPlanConfigSerializer, MediationCaseQuerySerializer, MediationCaseWechatSerializer
from apps.user.models import SystemUser
from utils.permission_helper import WechatFaceAuthPermission
from utils.ajax_result import AjaxResult
from utils.expression_calculator import calculate_expression_with_asset_data

# 获取日志记录器
logger = logging.getLogger(__name__)


class MediationCaseByDebtorView(GenericAPIView):
    """
    调解案件按债务人查询视图

    根据债务人姓名和身份证号查询该债务人相关的调解案件数量统计。

    **请求参数：**
    **查询参数：**
    - name (字符串, 必需): 债务人姓名
    - id_card (字符串, 必需): 债务人身份证号

    **响应数据结构：**
    ```json
    {
        "code": 200,
        "msg": "查询成功",
        "state": "success",
        "data": "已查询到【3】个相关调解案件"
    }
    ```

    **错误响应：**
    ```json
    {
        "code": 404,
        "msg": "未找到对应的债务人信息",
        "state": "fail"
    }
    ```
    """
    
    # 无需身份认证
    authentication_classes = []
    # 无需权限认证
    permission_classes = []
    
    serializer_class = MediationCaseByDebtorSerializer
    
    def get(self, request, *args, **kwargs):
        """
        根据债务人姓名和身份证号查询相关调解案件数量

        接收 name 和 id_card 查询参数，返回该债务人相关的调解案件数量统计。
        """
        # 获取查询参数
        name = request.query_params.get('name')
        id_card = request.query_params.get('id_card')
        
        # 验证参数是否提供
        if not name:
            return AjaxResult.fail(msg="请提供债务人姓名参数")
        if not id_card:
            return AjaxResult.fail(msg="请提供债务人身份证号参数")
        
        try:
            # 根据姓名和身份证号查询债务人
            debtor = DebtorBasicInfo.objects.get(debtor_name=name, id_number=id_card)
            
            # 统计该债务人相关的调解案件数量
            case_count = MediationCase.objects.filter(debtor=debtor).count()
            
            # 构建响应数据
            result_message = f"已查询到【{case_count}】个相关调解案件"
            
            # 返回成功响应
            return AjaxResult.success(msg="查询成功", data=result_message)
            
        except DebtorBasicInfo.DoesNotExist:
            # 债务人不存在时返回404错误
            return AjaxResult(code=404, msg="未找到对应的债务人信息", state="fail").to_json_response()
        except Exception as e:
            # 其他异常处理
            return AjaxResult.fail(msg=f"查询失败：{str(e)}")


class MediationContentView(GenericAPIView):
    """
    调解信息内容获取视图

    基于GenericAPIView实现的调解信息内容获取功能，用于获取调解案件的配置信息并进行表达式计算。
    该视图通过调解案件ID查询对应的调解案件，获取关联资产包的mediation_config配置，
    并对配置中的表达式进行计算处理，返回包含计算结果的完整配置信息。
    """

    # 配置序列化器类
    serializer_class = MediationContentSerializer

    def get(self, request, mediation_case_id, *args, **kwargs):
        """
        获取调解信息内容

        根据传入的调解案件ID，查询对应的调解案件对象，获取关联资产包的mediation_config配置，
        并对配置中的每个对象进行表达式计算处理，将计算结果作为"value"字段添加到原对象中。
        同时收集调解案件和关联资产包的附件数据。

        **请求参数：**
        - mediation_case_id (整数, 必需): 调解案件ID，通过URL路径参数传递

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "获取成功",
            "state": "success",
            "data": {
                "case_id": 1,
                "mediation_config": [
                    {
                        "id": "GZTJ6dz1z75km",
                        "title": "金额",
                        "logic_type": "result_calculation",
                        "expression": "{债权总额}-{本金余额}",
                        "value": "150000.00"
                    }
                ],
                "attachments": [
                    {
                        "id": "附件ID",
                        "name": "附件名称",
                        "url": "附件URL",
                        "type": "附件类型",
                        "source": "case|asset_package"
                    }
                ]
            }
        }
        ```

        **错误响应示例：**
        ```json
        {
            "code": 404,
            "msg": "未找到对应的调解案件",
            "state": "fail"
        }
        ```
        """
        try:
            # 验证调解案件ID参数
            serializer_data = {"mediation_case_id": mediation_case_id}
            serializer = self.get_serializer(data=serializer_data)
            serializer.is_valid(raise_exception=True)

            # 记录请求日志
            logger.info(f"开始处理调解信息内容获取，调解案件ID: {mediation_case_id}")

            # 获取调解案件对象
            mediation_case = MediationCase.objects.get(id=mediation_case_id)

            # 检查调解案件是否关联资产包
            if not mediation_case.asset_package:
                logger.error(f"调解案件 {mediation_case_id} 没有关联资产包")
                return AjaxResult.fail(msg="调解案件没有关联资产包")

            # 获取资产包的mediation_config配置
            mediation_config = mediation_case.asset_package.mediation_config
            if not mediation_config:
                logger.error(f"调解案件 {mediation_case_id} 关联的资产包没有mediation_config配置")
                return AjaxResult.fail(msg="调解案件关联的资产包没有调解配置信息")

            # 检查mediation_config是否为列表格式
            if not isinstance(mediation_config, list):
                logger.error(f"调解案件 {mediation_case_id} 的mediation_config不是列表格式")
                return AjaxResult.fail(msg="调解配置信息格式错误")

            # 处理mediation_config中的每个对象
            processed_config = []
            asset_package = mediation_case.asset_package

            # 获取调解案件在资产包中的行号，默认为1
            row_number = mediation_case.asset_package_row_number or 1

            for config_item in mediation_config:
                # 复制原始配置对象
                processed_item = config_item.copy()

                # 检查配置对象是否包含必要字段
                if not isinstance(config_item, dict):
                    logger.warning(f"跳过非字典格式的配置项: {config_item}")
                    processed_config.append(processed_item)
                    continue

                logic_type = config_item.get('logic_type')
                expression = config_item.get('expression')

                # 如果没有logic_type或expression，直接添加到结果中
                if not logic_type or not expression:
                    logger.warning(f"配置项缺少logic_type或expression字段: {config_item}")
                    processed_item['value'] = None
                    processed_config.append(processed_item)
                    continue

                # 使用工具函数计算表达式
                calculation_result = calculate_expression_with_asset_data(
                    asset_package=asset_package,
                    row_number=row_number,
                    expression=expression,
                    logic_type=logic_type
                )

                # 将计算结果添加到配置对象中
                if calculation_result['success']:
                    processed_item['value'] = calculation_result['result']
                    logger.info(f"表达式计算成功，配置ID: {config_item.get('id', 'unknown')}, 结果: {calculation_result['result']}")
                else:
                    processed_item['value'] = None
                    logger.error(f"表达式计算失败，配置ID: {config_item.get('id', 'unknown')}, 错误: {calculation_result['error']}")

                processed_config.append(processed_item)

            # 收集附件数据
            attachments_data = []

            # 收集调解案件的附件
            case_attachments = mediation_case.attachments.all()
            for attachment in case_attachments:
                attachment_info = {
                    "id": attachment.id,
                    "name": attachment.file_name,
                    "url": attachment.file.url if attachment.file else None,
                    "type": self._get_file_type(attachment.file_name) if attachment.file_name else None,
                    "source": "case"
                }
                attachments_data.append(attachment_info)
                logger.info(f"添加调解案件附件: {attachment.file_name}, ID: {attachment.id}")

            # 收集关联资产包的附件
            if asset_package:
                package_attachments = asset_package.attachments.all()
                for attachment in package_attachments:
                    attachment_info = {
                        "id": attachment.id,
                        "name": attachment.file_name,
                        "url": attachment.file.url if attachment.file else None,
                        "type": self._get_file_type(attachment.file_name) if attachment.file_name else None,
                        "source": "asset_package"
                    }
                    attachments_data.append(attachment_info)
                    logger.info(f"添加资产包附件: {attachment.file_name}, ID: {attachment.id}")

            # 构建响应数据结构
            response_data = {
                "case_id": mediation_case_id,
                "mediation_config": processed_config,
                "attachments": attachments_data
            }

            logger.info(f"调解信息内容处理完成，调解案件ID: {mediation_case_id}, 处理了 {len(processed_config)} 个配置项, {len(attachments_data)} 个附件")
            return AjaxResult.success(msg="获取成功", data=response_data)

        except serializers.ValidationError as e:
            # 处理序列化器验证错误
            error_messages = []
            if hasattr(e, 'detail'):
                for _, messages in e.detail.items():
                    if isinstance(messages, list):
                        error_messages.extend([str(msg) for msg in messages])
                    else:
                        error_messages.append(str(messages))
            error_msg = '; '.join(error_messages) if error_messages else str(e)
            logger.error(f"参数验证失败: {error_msg}")
            return AjaxResult.fail(msg=f"参数验证失败: {error_msg}")

        except MediationCase.DoesNotExist:
            logger.error(f"调解案件 {mediation_case_id} 不存在")
            return AjaxResult(code=404, msg="未找到对应的调解案件", state="fail").to_json_response()

        except Exception as e:
            logger.error(f"处理调解信息内容获取请求时发生异常: {str(e)}")
            return AjaxResult.fail(msg="服务器内部错误")

    def _get_file_type(self, file_name):
        """
        根据文件名获取文件类型

        Args:
            file_name (str): 文件名

        Returns:
            str: 文件类型
        """
        if not file_name:
            return "unknown"

        # 获取文件扩展名
        file_extension = file_name.lower().split('.')[-1] if '.' in file_name else ""

        # 定义文件类型映射
        type_mapping = {
            'pdf': 'document',
            'doc': 'document',
            'docx': 'document',
            'xls': 'spreadsheet',
            'xlsx': 'spreadsheet',
            'txt': 'text',
            'jpg': 'image',
            'jpeg': 'image',
            'png': 'image',
            'gif': 'image',
        }

        return type_mapping.get(file_extension, "unknown")


class MediationPlanConfigView(GenericAPIView):
    """
    调解方案配置获取视图

    基于GenericAPIView实现的调解方案配置获取功能，用于获取调解案件相关的调解方案配置信息并进行表达式计算。
    该视图通过调解案件ID查询对应的调解案件，获取所有相关的已生效调解方案的plan_config配置，
    并对配置中的表达式进行计算处理，返回按调解方案分组的嵌套结构数据。
    每个方案包含plan_id、plan_name和已计算的plan_config配置项数组。
    """

    # 配置序列化器类
    serializer_class = MediationPlanConfigSerializer

    def get(self, request, mediation_case_id, *args, **kwargs):
        """
        获取调解方案配置信息

        根据传入的调解案件ID，查询对应的调解案件对象，获取所有相关的已生效调解方案的plan_config配置，
        并对配置中的每个对象进行表达式计算处理，将计算结果作为"value"字段添加到原配置对象中。

        **请求参数：**
        - mediation_case_id (整数, 必需): 调解案件ID，通过URL路径参数传递

        **查询条件：**
        - asset_package=调解案件.asset_package 或 mediation_case=调解案件对象
        - plan_status="active" (已生效)

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "获取成功",
            "state": "success",
            "data": [
                {
                    "plan_id": 1,
                    "plan_name": "标准还款方案",
                    "plan_config": [
                        {
                            "id": "PLAN001",
                            "title": "还款方案",
                            "logic_type": "result_calculation",
                            "expression": "{债权总额}*0.8",
                            "value": "120000.00"
                        }
                    ]
                },
                {
                    "plan_id": 2,
                    "plan_name": "优惠还款方案",
                    "plan_config": [
                        {
                            "id": "PLAN002",
                            "title": "优惠方案",
                            "logic_type": "result_calculation",
                            "expression": "{债权总额}*0.6",
                            "value": "90000.00"
                        }
                    ]
                }
            ]
        }
        ```

        **错误响应示例：**
        ```json
        {
            "code": 404,
            "msg": "未找到对应的调解案件",
            "state": "fail"
        }
        ```
        """
        try:
            # 验证调解案件ID参数
            serializer_data = {"mediation_case_id": mediation_case_id}
            serializer = self.get_serializer(data=serializer_data)
            serializer.is_valid(raise_exception=True)

            # 记录请求日志
            logger.info(f"开始处理调解方案配置获取，调解案件ID: {mediation_case_id}")

            # 获取调解案件对象
            mediation_case = MediationCase.objects.get(id=mediation_case_id)

            # 查询相关的已生效调解方案
            # 查询条件：(asset_package=调解案件.asset_package 或 mediation_case=调解案件对象) 且 plan_status="active"
            query_conditions = Q(plan_status="active")

            if mediation_case.asset_package:
                query_conditions &= (Q(asset_package=mediation_case.asset_package) | Q(mediation_case=mediation_case))
            else:
                query_conditions &= Q(mediation_case=mediation_case)

            mediation_plans = MediationPlan.objects.filter(query_conditions)

            # 检查是否找到相关的调解方案
            if not mediation_plans.exists():
                logger.warning(f"调解案件 {mediation_case_id} 没有找到相关的已生效调解方案")
                return AjaxResult.success(msg="获取成功", data=[])

            # 按调解方案分组处理配置信息
            grouped_plan_configs = []
            asset_package = mediation_case.asset_package

            # 获取调解案件在资产包中的行号，默认为1
            row_number = mediation_case.asset_package_row_number or 1

            # 遍历每个调解方案
            for plan in mediation_plans:
                # 创建方案分组结构
                plan_group = {
                    "plan_id": plan.id,
                    "plan_name": plan.plan_name,
                    "plan_config": []
                }

                # 处理当前方案的plan_config配置
                if plan.plan_config:
                    # 获取配置项列表
                    config_items = []
                    if isinstance(plan.plan_config, list):
                        config_items = plan.plan_config
                    elif isinstance(plan.plan_config, dict):
                        config_items = [plan.plan_config]
                    else:
                        logger.warning(f"调解方案 {plan.id} 的plan_config格式不正确: {type(plan.plan_config)}")
                        continue

                    # 处理当前方案的每个配置项
                    for config_item in config_items:
                        # 复制原始配置对象
                        processed_item = config_item.copy() if isinstance(config_item, dict) else config_item

                        # 检查配置对象是否包含必要字段
                        if not isinstance(config_item, dict):
                            logger.warning(f"跳过非字典格式的配置项: {config_item}")
                            plan_group["plan_config"].append(processed_item)
                            continue

                        logic_type = config_item.get('logic_type')
                        expression = config_item.get('expression')

                        # 如果没有logic_type或expression，直接添加到结果中
                        if not logic_type or not expression:
                            logger.warning(f"配置项缺少logic_type或expression字段: {config_item}")
                            processed_item['value'] = None
                            plan_group["plan_config"].append(processed_item)
                            continue

                        # 检查是否有关联的资产包用于表达式计算
                        if not asset_package:
                            logger.warning(f"调解案件 {mediation_case_id} 没有关联资产包，无法进行表达式计算")
                            processed_item['value'] = None
                            plan_group["plan_config"].append(processed_item)
                            continue

                        # 使用工具函数计算表达式
                        calculation_result = calculate_expression_with_asset_data(
                            asset_package=asset_package,
                            row_number=row_number,
                            expression=expression,
                            logic_type=logic_type
                        )

                        # 将计算结果添加到配置对象中
                        if calculation_result['success']:
                            processed_item['value'] = calculation_result['result']
                            logger.info(f"表达式计算成功，方案ID: {plan.id}, 配置ID: {config_item.get('id', 'unknown')}, 结果: {calculation_result['result']}")
                        else:
                            processed_item['value'] = None
                            logger.error(f"表达式计算失败，方案ID: {plan.id}, 配置ID: {config_item.get('id', 'unknown')}, 错误: {calculation_result['error']}")

                        plan_group["plan_config"].append(processed_item)

                # 将方案分组添加到结果中（即使配置为空也添加，保持数据完整性）
                grouped_plan_configs.append(plan_group)

            # 统计处理的配置项总数
            total_config_count = sum(len(group["plan_config"]) for group in grouped_plan_configs)
            logger.info(f"调解方案配置处理完成，调解案件ID: {mediation_case_id}, 处理了 {len(grouped_plan_configs)} 个方案，共 {total_config_count} 个配置项")
            return AjaxResult.success(msg="获取成功", data=grouped_plan_configs)

        except serializers.ValidationError as e:
            # 处理序列化器验证错误
            error_messages = []
            if hasattr(e, 'detail'):
                for _, messages in e.detail.items():
                    if isinstance(messages, list):
                        error_messages.extend([str(msg) for msg in messages])
                    else:
                        error_messages.append(str(messages))
            error_msg = '; '.join(error_messages) if error_messages else str(e)
            logger.error(f"参数验证失败: {error_msg}")
            return AjaxResult.fail(msg=f"参数验证失败: {error_msg}")

        except MediationCase.DoesNotExist:
            logger.error(f"调解案件 {mediation_case_id} 不存在")
            return AjaxResult(code=404, msg="未找到对应的调解案件", state="fail").to_json_response()

        except Exception as e:
            logger.error(f"处理调解方案配置获取请求时发生异常: {str(e)}")
            return AjaxResult.fail(msg="服务器内部错误")


class MediationCaseListAPIView(GenericAPIView):
    """
    微信用户调解案件列表查询视图

    基于GenericAPIView实现的微信用户调解案件查询功能，用于微信小程序端用户查询自己相关的调解案件。
    该视图通过微信OpenID查询对应的SystemUser，验证用户认证状态，然后基于用户的真实姓名和身份证号
    查询相关的调解案件信息。支持查询所有案件或指定案件ID的单个案件。

    **请求参数：**
    - mediation_case_id (整数, 可选): 调解案件ID，用于查询指定的调解案件

    **业务逻辑：**
    1. 通过WechatFaceAuthPermission权限验证用户身份和人脸核身状态
    2. 从已认证的用户获取real_name和id_card_number
    3. 使用用户的real_name和id_card_number查询DebtorBasicInfo对象
    4. 通过DebtorBasicInfo查询相关的MediationCase对象
    5. 根据mediation_case_id参数进行过滤（如果提供）

    **返回数据：**
    - 调解案件列表，包含案件ID、案件号、状态、发起日期等信息
    - 如果提供mediation_case_id，返回指定案件（仍以列表格式）
    - 找不到数据时返回空列表

    **错误处理：**
    - 用户未认证时返回认证失败错误
    - 找不到用户或债务人信息时返回相应错误
    """

    # 使用微信人脸核身权限认证
    permission_classes = [WechatFaceAuthPermission]

    serializer_class = MediationCaseQuerySerializer

    def get(self, request, *args, **kwargs):
        """
        获取微信用户相关的调解案件列表

        通过WechatFaceAuthPermission权限验证用户身份和人脸核身状态，
        然后基于已认证用户的真实姓名和身份证号查询相关的调解案件信息。

        **请求参数：**
        - mediation_case_id (整数, 可选): 调解案件ID

        **响应数据结构：**
        ```json
        {
            "code": 200,
            "msg": "查询成功",
            "state": "success",
            "data": [
                {
                    "id": 1,
                    "case_number": "GZTJ20250731ABC123",
                    "case_status": "in_progress",
                    "case_status_cn": "进行中",
                    "initiate_date": "2025-07-31"
                }
            ]
        }
        ```

        **错误响应示例：**
        ```json
        {
            "code": 401,
            "msg": "用户未通过人脸核身认证",
            "state": "fail"
        }
        ```
        """
        try:
            # 获取查询参数
            mediation_case_id = request.query_params.get('mediation_case_id')

            # 使用序列化器验证参数
            serializer = self.get_serializer(data={
                'mediation_case_id': mediation_case_id
            })
            serializer.is_valid(raise_exception=True)

            # 获取验证后的参数
            validated_data = serializer.validated_data
            mediation_case_id = validated_data.get('mediation_case_id')

            # 通过权限验证后，直接从request.user获取用户信息
            # WechatFaceAuthPermission已确保用户通过认证且人脸核身成功
            try:
                system_user = SystemUser.objects.get(username=request.user.username)
            except SystemUser.DoesNotExist:
                logger.error(f"权限验证通过但未找到SystemUser记录: {request.user.username}")
                return AjaxResult.fail(msg="用户信息异常，请重新登录")

            # 获取用户的真实姓名和身份证号
            real_name = system_user.real_name
            id_card_number = system_user.id_card_number

            if not real_name or not id_card_number:
                logger.warning(f"用户缺少必要的身份信息: {request.user.username}, real_name: {real_name}, id_card_number: {bool(id_card_number)}")
                return AjaxResult.fail(msg="用户身份信息不完整")

            # 根据姓名和身份证号查询债务人信息
            try:
                debtor = DebtorBasicInfo.objects.get(debtor_name=real_name, id_number=id_card_number)
            except DebtorBasicInfo.DoesNotExist:
                logger.info(f"未找到对应的债务人信息: {real_name}, {id_card_number[:6]}****")
                return AjaxResult.success(msg="查询成功", data=[])

            # 查询相关的调解案件
            mediation_cases_query = MediationCase.objects.filter(debtor=debtor)

            # 如果提供了mediation_case_id，进行过滤
            if mediation_case_id is not None:
                mediation_cases_query = mediation_cases_query.filter(id=mediation_case_id)

            # 获取案件列表并按创建时间倒序排列
            mediation_cases = mediation_cases_query.order_by('-created_time')

            # 使用序列化器构建返回数据
            serializer = MediationCaseWechatSerializer(mediation_cases, many=True)
            case_list = serializer.data

            # 记录查询结果
            logger.info(f"微信用户调解案件查询成功: {request.user.username}, 找到 {len(case_list)} 个案件")

            return AjaxResult.success(msg="查询成功", data=case_list)

        except serializers.ValidationError as e:
            # 处理序列化器验证错误
            error_messages = []
            if hasattr(e, 'detail'):
                for _, messages in e.detail.items():
                    if isinstance(messages, list):
                        error_messages.extend([str(msg) for msg in messages])
                    else:
                        error_messages.append(str(messages))
            error_msg = '; '.join(error_messages) if error_messages else str(e)
            logger.error(f"参数验证失败: {error_msg}")
            return AjaxResult.fail(msg=f"参数验证失败: {error_msg}")

        except Exception as e:
            logger.error(f"处理微信用户调解案件查询请求时发生异常: {str(e)}")
            return AjaxResult.fail(msg="服务器内部错误")
