from django.db import models
from django.conf import settings
from ops_management.base_model import BaseModel
from apps.counterparty.models import CreditorBasicInfo
from apps.user.models import SystemUser


class AssetPackageFieldConfig(BaseModel):
    """资产包字段配置模型"""

    # 字段类型选择项
    FIELD_TYPE_CHOICES = [
        ("text", "文本类型"),
        ("date", "日期类型"),
        ("numeric", "数值类型"),  # 新增：用于存储数字数据（整数或浮点数）
        ("amount", "金额类型"),   # 新增：用于存储货币金额数据
    ]

    # 数据校验选择项
    VALIDATION_CHOICES = [
        ("none", "无校验"),
        ("phone", "手机号格式校验"),
        ("id_card", "身份证格式校验"),
        ("social_credit_code", "统一社会信用代码校验"),
    ]

    # 字段定义
    field_name = models.CharField(max_length=100, blank=True, null=True, unique=True, verbose_name="字段名称")
    field_type = models.CharField(
        max_length=20, choices=FIELD_TYPE_CHOICES, blank=True, null=True, verbose_name="字段类型"
    )
    data_validation = models.CharField(
        max_length=20,
        choices=VALIDATION_CHOICES,
        blank=True,
        null=True,
        verbose_name="数据校验",
        help_text="选择字段数据的校验规则：无校验、手机号格式校验、身份证格式校验、统一社会信用代码校验",
    )
    is_masked = models.BooleanField(default=False, verbose_name="是否脱敏")
    prefix_keep_chars = models.PositiveIntegerField(default=0, verbose_name="前保留字符数")
    suffix_keep_chars = models.PositiveIntegerField(default=0, verbose_name="后保留字符数")
    display_order = models.PositiveIntegerField(default=100, verbose_name="显示顺序")

    class Meta:
        verbose_name = "资产包字段配置"
        verbose_name_plural = "资产包字段配置"
        db_table = "t_asset_package_field_config"

    def __str__(self):
        return f"{self.field_name} ({self.get_field_type_display()})"


class AssetPackageFieldMapping(BaseModel):
    """资产包字段映射模型 - 用于映射原始Excel表头字段到标准字段配置"""

    # 债务人字段配置选择项 - 从 DebtorBasicInfo 模型字段生成（排除 BaseModel 继承字段）
    DEBTOR_FIELD_CHOICES = [
        ("debtor_type", "债务人类型"),
        ("debtor_name", "债务人名称"),
        ("id_type", "证件类型"),
        ("id_number", "证件号码"),
        ("phones", "联系方式-电话"),
        ("emails", "联系方式-邮箱"),
        ("addresses", "联系方式-地址"),
        ("wechats", "联系方式-微信"),
    ]

    # 原表头字段名称 - 来自上传的Excel文件的表头
    original_field_name = models.CharField(
        max_length=255, verbose_name="原表头字段名称", help_text="Excel文件中的原始表头字段名称"
    )
    # 映射的字段配置 - 关联到标准字段配置，允许为空（实际业务中不是每个Excel表头字段都需要映射）
    mapped_field_config = models.ForeignKey(
        AssetPackageFieldConfig,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="映射字段配置",
        help_text="映射到的标准字段配置，允许为空",
    )
    # 映射的债务人字段配置 - 选择债务人模型中的具体字段
    mapped_debtor_field_config = models.CharField(
        max_length=50,
        choices=DEBTOR_FIELD_CHOICES,
        blank=True,
        null=True,
        verbose_name="映射的债务人字段配置",
        help_text="选择映射到债务人模型中的具体字段，用于数据导入时的字段对应关系",
    )

    class Meta:
        verbose_name = "资产包字段映射"
        verbose_name_plural = "资产包字段映射"
        db_table = "t_asset_package_field_mapping"

    def __str__(self):
        mapped_name = self.mapped_field_config.field_name if self.mapped_field_config else "未映射"
        return f"{self.original_field_name} -> {mapped_name}"


class AssetPackageManagementFile(BaseModel):
    """资产包管理附件模型 - 管理资产包相关的附件文件"""

    # 附件文件 - 实际存储的文件
    file = models.FileField(upload_to=settings.UPLOAD_DIR, verbose_name="附件文件", help_text="资产包相关的附件文件")

    # 附件原名 - 上传文件的原始名称
    file_name = models.CharField(max_length=255, verbose_name="附件原名", help_text="上传文件的原始名称")

    class Meta:
        db_table = "t_asset_package_management_file"
        verbose_name = "资产包管理附件"
        verbose_name_plural = "资产包管理附件"

    def __str__(self):
        return self.file_name or f"附件-{self.id}"


class AssetPackageManagement(BaseModel):
    """资产包管理模型 - 管理上传的资产包文件及其字段映射关系"""

    # 资产包状态选择项
    PACKAGE_STATUS_CHOICES = [
        ("available", "可用"),
        ("unavailable", "不可用"),
    ]

    # 资产包名称
    package_name = models.CharField(max_length=255, verbose_name="资产包名称", help_text="资产包的业务名称标识")
    # 原文件 - 上传的Excel文件
    source_file = models.FileField(
        upload_to=settings.UPLOAD_DIR, verbose_name="原文件", help_text="上传的资产包Excel文件"
    )
    # 原文件大小 - 以字节为单位
    file_size = models.BigIntegerField(null=True, blank=True, verbose_name="原文件大小", help_text="文件大小(字节)")
    # 上传人 - 关联SystemUser模型
    uploader = models.ForeignKey(
        SystemUser, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="上传人", help_text="上传文件的用户"
    )
    # 上传时间 - 记录文件上传或重新上传的时间戳
    upload_time = models.DateTimeField(
        null=True, blank=True, verbose_name="上传时间", help_text="文件上传或重新上传的时间戳"
    )
    # 债权人 - 关联债权人基本信息，用于标识该资产包所属的债权人
    creditor = models.ForeignKey(
        CreditorBasicInfo,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="债权人",
        help_text="关联的债权人信息",
    )
    # 资产包可用状态 - 基于数据验证结果自动设置
    package_status = models.CharField(
        max_length=20,
        choices=PACKAGE_STATUS_CHOICES,
        default="unavailable",  # 修改默认值为不可用，新创建的资产包需要配置后才能使用
        verbose_name="资产包状态",
        help_text="资产包的可用状态，基于数据验证结果自动设置",
    )
    # 不可用原因 - 存储数据验证失败的详细信息
    unavailable_reason = models.TextField(
        null=True, blank=True, verbose_name="不可用原因", help_text="当资产包状态为不可用时，存储具体的数据验证失败信息"
    )
    # 字段映射关系 - 多对多关联字段映射
    field_mappings = models.ManyToManyField(
        AssetPackageFieldMapping, blank=True, verbose_name="字段映射", help_text="该资产包的字段映射配置"
    )
    # 附件文件 - 多对多关联附件文件
    attachments = models.ManyToManyField(
        AssetPackageManagementFile, blank=True, verbose_name="附件文件", help_text="该资产包的相关附件文件"
    )
    # 调解信息配置 - JSON格式的调解配置信息
    mediation_config = models.JSONField(
        blank=True, null=True, verbose_name="调解信息配置", help_text="JSON格式的调解配置信息"
    )

    class Meta:
        verbose_name = "资产包管理"
        verbose_name_plural = "资产包管理"
        db_table = "t_asset_package_management"

    def __str__(self):
        return self.package_name or f"资产包-{self.id}"
