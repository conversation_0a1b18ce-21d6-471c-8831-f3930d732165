#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : mediation_case_query_serializers.py
<AUTHOR> JT_DA
@Date     : 2025/07/22
@File_Desc: 调解案件查询相关序列化器
"""

from rest_framework import serializers
from apps.mediation_management.models import MediationCase


class MediationCaseByNumberSerializer(serializers.ModelSerializer):
    """调解案件按案件号查询序列化器 - 用于根据案件号查询案件基本信息"""

    # 案件状态中文显示
    case_status_cn = serializers.CharField(source="get_case_status_display", read_only=True)

    class Meta:
        model = MediationCase
        fields = [
            "id",                # 调解案件ID
            "case_number",       # 调解案件号
            "case_status",       # 案件状态
            "case_status_cn",    # 案件状态中文显示
            "initiate_date",     # 发起日期
        ]


class MediationCaseByDebtorSerializer(serializers.Serializer):
    """调解案件按债务人查询序列化器 - 用于验证债务人姓名和身份证号参数"""

    name = serializers.CharField(
        required=True,
        max_length=200,
        help_text="债务人姓名，用于查询对应的债务人对象"
    )
    
    id_card = serializers.CharField(
        required=True,
        max_length=50,
        help_text="债务人身份证号，用于查询对应的债务人对象"
    )


class MediationContentSerializer(serializers.Serializer):
    """调解信息内容序列化器 - 用于验证调解案件ID参数"""

    mediation_case_id = serializers.IntegerField(
        required=True,
        help_text="调解案件ID，用于查询对应的调解案件对象"
    )

    def validate_mediation_case_id(self, value):
        """验证调解案件ID是否存在"""
        try:
            MediationCase.objects.get(id=value)
        except MediationCase.DoesNotExist:
            raise serializers.ValidationError("指定的调解案件不存在")
        return value


class MediationPlanConfigSerializer(serializers.Serializer):
    """调解方案配置序列化器 - 用于验证调解案件ID参数"""

    mediation_case_id = serializers.IntegerField(
        required=True,
        help_text="调解案件ID，用于查询对应的调解案件对象"
    )

    def validate_mediation_case_id(self, value):
        """验证调解案件ID是否存在"""
        try:
            MediationCase.objects.get(id=value)
        except MediationCase.DoesNotExist:
            raise serializers.ValidationError("指定的调解案件不存在")
        return value


class MediationCaseQuerySerializer(serializers.Serializer):
    """调解案件查询参数序列化器 - 用于验证微信用户调解案件查询参数"""

    mediation_case_id = serializers.IntegerField(
        required=False,
        help_text="调解案件ID，可选参数，用于查询指定的调解案件"
    )

    def validate_mediation_case_id(self, value):
        """验证调解案件ID是否存在"""
        if value is not None:
            try:
                MediationCase.objects.get(id=value)
            except MediationCase.DoesNotExist:
                raise serializers.ValidationError("指定的调解案件不存在")
        return value


class MediationCaseWechatSerializer(serializers.ModelSerializer):
    """微信端调解案件序列化器 - 用于微信小程序端返回简化的案件信息"""

    # 状态字段中文显示
    case_status_cn = serializers.CharField(source="get_case_status_display", read_only=True)

    # 时间字段格式化显示（仅显示日期）
    initiate_date = serializers.SerializerMethodField()

    class Meta:
        model = MediationCase
        fields = [
            "id",                # 调解案件ID
            "case_number",       # 调解案件号
            "case_status",       # 案件状态
            "case_status_cn",    # 案件状态中文显示
            "initiate_date",     # 发起日期
        ]

    def get_initiate_date(self, obj):
        """格式化发起日期为YYYY-MM-DD格式"""
        if obj.initiate_date:
            return obj.initiate_date.strftime('%Y-%m-%d')
        return None
